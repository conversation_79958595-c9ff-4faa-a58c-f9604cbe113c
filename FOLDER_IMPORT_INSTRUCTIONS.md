# 📁 Folder Import Feature - Testing Instructions

## How to Test the New Folder Import Feature

### 1. Access the Application
- Open your browser to: `http://localhost:5173/`
- You should see the updated interface with two input method options

### 2. Test File Upload
1. **Click "📁 Upload Files"** button to switch to file upload mode
2. **Click the upload area** (dashed border box)
3. **Select multiple files** from the `sample-resumes/` folder:
   - `john-developer.txt` (Software Developer)
   - `emily-designer.txt` (UX/UI Designer) 
   - `michael-manager.txt` (Project Manager)
4. **Observe** that all files appear in the "Uploaded Files" list
5. **Click different files** to switch between them and see the content change

### 3. Test Conversation with Multiple Resumes
1. **Click "🚀 Start Conversation"** after uploading files
2. **Ask questions** about the currently selected resume
3. **Use the dropdown** in the conversation header to switch between resumes
4. **Notice** that each resume maintains its own conversation context

### 4. Sample Questions to Test
Try these questions with different resumes:

**For <PERSON> (Developer):**
- "What programming languages does <PERSON> know?"
- "How many years of experience does he have?"
- "Does he have team leadership experience?"
- "What cloud platforms is he certified in?"

**For <PERSON> (Designer):**
- "What design tools does Emily use?"
- "What awards has she won?"
- "Does she have user research experience?"
- "What's her educational background?"

**For Michael (Manager):**
- "How many projects does Michael manage concurrently?"
- "What's his project delivery success rate?"
- "What certifications does he have?"
- "What's his team size?"

### 5. Features to Test
- ✅ **File Upload**: Multiple .txt file selection
- ✅ **File Switching**: Click files in the list to switch
- ✅ **Resume Dropdown**: Switch resumes during conversation
- ✅ **Conversation Reset**: Each resume gets fresh conversation
- ✅ **Responsive Design**: Test on mobile/tablet
- ✅ **Input Method Toggle**: Switch between text paste and file upload

### 6. Expected Behavior
- **File List**: Shows all uploaded files with sizes
- **Selected File**: Highlighted with checkmark
- **Content Preview**: Resume text updates when switching files
- **Conversation Context**: AI remembers which resume is being discussed
- **Dropdown**: Only appears when multiple files are uploaded
- **Reset**: Clears all files and conversations

### 7. File Format Support
- **Supported**: `.txt` files (plain text)
- **Not Supported**: PDF, DOCX, etc. (shows in browser file picker but won't process)

### 8. Error Handling
- **No Files**: Button disabled until files uploaded or text pasted
- **Invalid Files**: Silently skips non-text files
- **Empty Files**: Handles gracefully

## Benefits of Folder Import
1. **Bulk Processing**: Upload multiple resumes at once
2. **Easy Comparison**: Switch between candidates quickly
3. **Efficient Workflow**: Perfect for HR professionals and recruiters
4. **Context Preservation**: Each resume maintains separate conversation history
5. **File Management**: Clear visual indication of selected resume

This feature transforms the Resume Finder from a single-resume analyzer into a powerful multi-resume management and analysis tool!

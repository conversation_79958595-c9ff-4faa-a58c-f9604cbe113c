# 📁 Enhanced File & Folder Import - Testing Instructions

## How to Test the Enhanced Multi-Format File Import Feature

### 1. Access the Application
- Open your browser to: `http://localhost:5173/`
- You should see the updated interface with two input method options

### 2. Test Multi-Format File Upload
1. **Click "📁 Upload Files"** button to switch to file upload mode
2. **Click the upload area** (dashed border box with 📄 icon)
3. **Select multiple files** from the `sample-resumes/` folder:
   - `john-developer.txt` (Software Developer - TXT format)
   - `emily-designer.txt` (UX/UI Designer - TXT format)
   - `michael-manager.txt` (Project Manager - TXT format)
   - `sarah-analyst.txt` (Data Analyst - TXT format)
4. **Test different file formats** if you have them:
   - Upload .docx Word documents
   - Upload .pdf files
5. **Observe** that all files appear in the "Uploaded Files" list with format badges
6. **Click different files** to switch between them and see the content change

### 3. Test Folder Upload
1. **Click the folder upload area** (dashed border box with 📁 icon)
2. **Select an entire folder** containing resume files
3. **Watch the processing indicator** as files are parsed
4. **Verify** all supported files (.txt, .docx, .pdf) are processed

### 3. Test Conversation with Multiple Resumes
1. **Click "🚀 Start Conversation"** after uploading files
2. **Ask questions** about the currently selected resume
3. **Use the dropdown** in the conversation header to switch between resumes
4. **Notice** that each resume maintains its own conversation context

### 4. Sample Questions to Test
Try these questions with different resumes:

**For John (Developer):**
- "What programming languages does John know?"
- "How many years of experience does he have?"
- "Does he have team leadership experience?"
- "What cloud platforms is he certified in?"

**For Emily (Designer):**
- "What design tools does Emily use?"
- "What awards has she won?"
- "Does she have user research experience?"
- "What's her educational background?"

**For Michael (Manager):**
- "How many projects does Michael manage concurrently?"
- "What's his project delivery success rate?"
- "What certifications does he have?"
- "What's his team size?"

### 5. Features to Test
- ✅ **Multi-Format Upload**: .txt, .docx, and .pdf file support
- ✅ **Folder Upload**: Select entire folders containing resume files
- ✅ **File Processing**: Watch the spinner during file parsing
- ✅ **Format Indicators**: See file format badges (TXT, DOCX, PDF)
- ✅ **File Switching**: Click files in the list to switch
- ✅ **Resume Dropdown**: Switch resumes during conversation
- ✅ **Conversation Reset**: Each resume gets fresh conversation
- ✅ **Responsive Design**: Test on mobile/tablet
- ✅ **Input Method Toggle**: Switch between text paste and file upload

### 6. Expected Behavior
- **File List**: Shows all uploaded files with sizes
- **Selected File**: Highlighted with checkmark
- **Content Preview**: Resume text updates when switching files
- **Conversation Context**: AI remembers which resume is being discussed
- **Dropdown**: Only appears when multiple files are uploaded
- **Reset**: Clears all files and conversations

### 7. File Format Support
- **✅ .txt files**: Direct text processing (fastest)
- **✅ .docx files**: Microsoft Word documents with automatic text extraction
- **✅ .pdf files**: PDF documents with advanced text extraction using PDF.js
- **❌ Other formats**: .doc, .rtf, .odt, etc. are not supported

### 8. Error Handling
- **No Files**: Button disabled until files uploaded or text pasted
- **Invalid Files**: Silently skips non-text files
- **Empty Files**: Handles gracefully

## Benefits of Folder Import
1. **Bulk Processing**: Upload multiple resumes at once
2. **Easy Comparison**: Switch between candidates quickly
3. **Efficient Workflow**: Perfect for HR professionals and recruiters
4. **Context Preservation**: Each resume maintains separate conversation history
5. **File Management**: Clear visual indication of selected resume

This feature transforms the Resume Finder from a single-resume analyzer into a powerful multi-resume management and analysis tool!

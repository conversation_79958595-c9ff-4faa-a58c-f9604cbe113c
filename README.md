# 🤖 Augment Code - Conversational Resume Finder

An intelligent AI-powered web application that acts as a conversational Resume Finder. Paste any resume text and ask questions about it in a natural, chat-like interface.

## Features

- **Conversational Interface**: Chat with the AI about resume content
- **Multiple Input Methods**: Paste text directly or upload multiple resume files
- **Folder Import**: Upload and analyze multiple resume files at once
- **Resume Switching**: Easily switch between different resumes during analysis
- **Intelligent Analysis**: AI understands context and provides relevant answers
- **Resume Parsing**: Automatically extracts key information from resume text
- **Follow-up Questions**: Maintains conversation context for deeper analysis
- **Modern UI**: Clean, responsive design with dark mode support

## How to Use

### Step 1: Choose Input Method
Select either "📝 Paste Text" or "📁 Upload Files" depending on how you want to provide resume data.

#### Option A: Paste Text
Copy and paste the full text content of any resume into the text area. You can use the sample resume provided in `sample-resume.txt` for testing.

#### Option B: Upload Files
Click the upload area to select multiple .txt resume files from your computer. You can upload an entire folder of resumes at once. Sample resume files are provided in the `sample-resumes/` folder.

### Step 2: Start Conversation
Click "🚀 Start Conversation with Resume Finder" to begin the AI analysis.

### Step 3: Ask Questions
Use the chat interface to ask questions about the resume. Examples:
- "What is the candidate's current job title and company?"
- "How many years of experience do they have?"
- "What are their key skills?"
- "Did they manage a team? If so, how many people?"
- "What is their education background?"
- "What companies have they worked at?"

### Step 4: Continue the Chat
The AI remembers the resume context throughout the conversation, so you can ask follow-up questions and get detailed responses.

### Step 5: Switch Between Resumes (if multiple uploaded)
If you uploaded multiple resume files, you can switch between them using the dropdown menu in the conversation header. Each resume maintains its own conversation context.

## Sample Questions to Try

- **Basic Info**: "What's the candidate's name and contact information?"
- **Experience**: "How many years of total experience does this person have?"
- **Current Role**: "What is their current position and responsibilities?"
- **Skills**: "What marketing channels are they proficient in?"
- **Management**: "Do they have team leadership experience?"
- **Education**: "What degrees do they have and from which institutions?"
- **Achievements**: "What specific results or achievements are mentioned?"
- **Career Progression**: "How has their career progressed over time?"

## Installation & Setup

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

### Running the Application
1. Open your terminal in the project directory
2. Run `npm run dev`
3. Open your browser to `http://localhost:5173`
4. Paste a resume and start chatting!

## Technology Stack

- **Frontend**: React 18 with Vite
- **Styling**: CSS3 with modern features
- **AI Logic**: Custom intelligent response system
- **Build Tool**: Vite for fast development and building

## Features in Detail

### Intelligent Response System
The AI Resume Finder uses pattern matching and context analysis to provide relevant answers:
- Extracts names, job titles, companies, and experience
- Identifies skills, education, and achievements
- Recognizes team management and leadership experience
- Provides contextual responses based on question intent

### Conversation Memory
- Maintains context throughout the chat session
- Remembers the resume content for all follow-up questions
- Provides consistent and relevant responses

### User Experience
- Clean, modern interface
- Real-time typing indicators
- Message timestamps
- Clickable example questions
- Responsive design for mobile and desktop
- Dark mode support

## Sample Resume

A sample resume (`sample-resume.txt`) is included for testing. It contains:
- Marketing Manager with 7+ years experience
- Team management experience
- Multiple companies and roles
- Education and certifications
- Specific achievements and metrics

## Future Enhancements

- Integration with real AI APIs (OpenAI, Claude, etc.)
- Resume file upload support (PDF, DOCX)
- Export conversation history
- Multiple resume comparison
- Advanced analytics and insights
- Resume scoring and recommendations

## Contributing

Feel free to contribute to this project by:
1. Forking the repository
2. Creating a feature branch
3. Making your changes
4. Submitting a pull request

## License

This project is open source and available under the MIT License.

import { useState, useRef } from 'react'
import './App.css'
import mammoth from 'mammoth'
import * as pdfjsLib from 'pdfjs-dist'

// Set up PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js`

function App() {
  const [resumeText, setResumeText] = useState('')
  const [conversationStarted, setConversationStarted] = useState(false)
  const [messages, setMessages] = useState([])
  const [currentQuestion, setCurrentQuestion] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState([])
  const [selectedResumeIndex, setSelectedResumeIndex] = useState(0)
  const [inputMethod, setInputMethod] = useState('text') // 'text' or 'files'
  const fileInputRef = useRef(null)

  // File handling functions
  const handleFileUpload = async (event) => {
    const files = Array.from(event.target.files)
    const processedFiles = []

    setIsProcessing(true)

    for (const file of files) {
      try {
        let text = ''
        const fileExtension = file.name.toLowerCase().split('.').pop()

        if (fileExtension === 'txt' || file.type === 'text/plain') {
          text = await readFileAsText(file)
        } else if (fileExtension === 'docx' || file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
          text = await readDocxFile(file)
        } else if (fileExtension === 'pdf' || file.type === 'application/pdf') {
          text = await readPdfFile(file)
        } else {
          console.warn(`Unsupported file type: ${file.name}`)
          continue
        }

        if (text.trim()) {
          processedFiles.push({
            name: file.name,
            content: text,
            size: file.size,
            type: file.type,
            format: fileExtension.toUpperCase()
          })
        }
      } catch (error) {
        console.error(`Error reading file ${file.name}:`, error)
      }
    }

    setIsProcessing(false)
    setUploadedFiles(processedFiles)
    if (processedFiles.length > 0) {
      setSelectedResumeIndex(0)
      setResumeText(processedFiles[0].content)
    }
  }

  const readFileAsText = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target.result)
      reader.onerror = (e) => reject(e)
      reader.readAsText(file)
    })
  }

  const readDocxFile = async (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = async (e) => {
        try {
          const arrayBuffer = e.target.result
          const result = await mammoth.extractRawText({ arrayBuffer })
          resolve(result.value)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = (e) => reject(e)
      reader.readAsArrayBuffer(file)
    })
  }

  const readPdfFile = async (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = async (e) => {
        try {
          const arrayBuffer = e.target.result
          const text = await extractTextFromPdfBuffer(arrayBuffer)
          resolve(text)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = (e) => reject(e)
      reader.readAsArrayBuffer(file)
    })
  }

  // PDF text extraction using PDF.js
  const extractTextFromPdfBuffer = async (arrayBuffer) => {
    try {
      const uint8Array = new Uint8Array(arrayBuffer)
      const pdf = await pdfjsLib.getDocument({ data: uint8Array }).promise

      let fullText = ''

      // Extract text from each page
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        const page = await pdf.getPage(pageNum)
        const textContent = await page.getTextContent()

        const pageText = textContent.items
          .map(item => item.str)
          .join(' ')
          .replace(/\s+/g, ' ')
          .trim()

        if (pageText) {
          fullText += pageText + '\n'
        }
      }

      if (fullText.trim().length === 0) {
        throw new Error('No text content found in PDF')
      }

      return fullText.trim()
    } catch (error) {
      console.error('PDF parsing error:', error)
      throw new Error(`PDF parsing failed: ${error.message}. Please ensure the PDF contains selectable text or convert to DOCX/TXT format.`)
    }
  }

  const handleResumeSelection = (index) => {
    setSelectedResumeIndex(index)
    setResumeText(uploadedFiles[index].content)
    if (conversationStarted) {
      // Reset conversation when switching resumes
      setConversationStarted(false)
      setMessages([])
    }
  }

  const triggerFileUpload = () => {
    fileInputRef.current?.click()
  }

  const handleFolderUpload = async (event) => {
    const files = Array.from(event.target.files)
    await handleFileUpload({ target: { files } })
  }

  const getFileIcon = (format) => {
    switch (format?.toUpperCase()) {
      case 'PDF':
        return '📕'
      case 'DOCX':
        return '📘'
      case 'TXT':
        return '📄'
      default:
        return '📄'
    }
  }

  // Mock AI Resume Finder - simulates intelligent responses
  const processResumeQuestion = async (question, resumeContext) => {
    setIsProcessing(true)
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Simple AI-like responses based on resume content and question
    let response = generateIntelligentResponse(question, resumeContext)
    
    setIsProcessing(false)
    return response
  }

  const generateIntelligentResponse = (question, resumeText) => {
    const lowerQuestion = question.toLowerCase()
    const resume = resumeText.toLowerCase()
    
    // Extract key information from resume
    const extractInfo = {
      name: extractName(resumeText),
      currentJob: extractCurrentJob(resumeText),
      experience: extractExperience(resumeText),
      skills: extractSkills(resumeText),
      education: extractEducation(resumeText),
      companies: extractCompanies(resumeText)
    }

    // Generate contextual responses
    if (lowerQuestion.includes('name')) {
      return extractInfo.name ? `The candidate's name is ${extractInfo.name}.` : "I couldn't find a clear name in the resume."
    }
    
    if (lowerQuestion.includes('current job') || lowerQuestion.includes('current position') || lowerQuestion.includes('current role')) {
      return extractInfo.currentJob || "I couldn't determine the current job position from the resume."
    }
    
    if (lowerQuestion.includes('experience') || lowerQuestion.includes('years')) {
      return extractInfo.experience || "I found experience information but couldn't determine specific years."
    }
    
    if (lowerQuestion.includes('skill') || lowerQuestion.includes('proficient') || lowerQuestion.includes('technology')) {
      return extractInfo.skills || "I found various skills mentioned in the resume."
    }
    
    if (lowerQuestion.includes('education') || lowerQuestion.includes('degree') || lowerQuestion.includes('university') || lowerQuestion.includes('college')) {
      return extractInfo.education || "I found education information in the resume."
    }
    
    if (lowerQuestion.includes('team') || lowerQuestion.includes('manage') || lowerQuestion.includes('lead')) {
      const teamInfo = extractTeamInfo(resumeText)
      return teamInfo || "I couldn't find specific information about team management in the resume."
    }
    
    if (lowerQuestion.includes('company') || lowerQuestion.includes('companies') || lowerQuestion.includes('employer')) {
      return extractInfo.companies || "I found several companies mentioned in the work history."
    }
    
    // Default intelligent response
    return `Based on the resume provided, I can help you find specific information. The resume contains details about the candidate's background, experience, and qualifications. Could you ask a more specific question about what you'd like to know?`
  }

  // Helper functions to extract information
  const extractName = (text) => {
    const lines = text.split('\n')
    const firstLine = lines[0]?.trim()
    if (firstLine && firstLine.length < 50 && !firstLine.includes('@') && !firstLine.includes('|')) {
      return firstLine
    }
    return null
  }

  const extractCurrentJob = (text) => {
    const lines = text.split('\n')
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].toLowerCase()
      if (line.includes('present') || line.includes('current')) {
        // Look for job title in previous lines
        for (let j = i - 1; j >= Math.max(0, i - 3); j--) {
          const prevLine = lines[j]?.trim()
          if (prevLine && prevLine.includes('|')) {
            const parts = prevLine.split('|')
            return `Currently working as ${parts[0]?.trim()} at ${parts[1]?.trim()}`
          }
        }
      }
    }
    return null
  }

  const extractExperience = (text) => {
    const experienceMatch = text.match(/(\d+)\+?\s*years?\s*(of\s*)?experience/i)
    if (experienceMatch) {
      return `The candidate has ${experienceMatch[1]}+ years of experience.`
    }
    return null
  }

  const extractSkills = (text) => {
    const skillKeywords = ['proficient', 'skills', 'technologies', 'expertise']
    const lines = text.split('\n')
    
    for (const line of lines) {
      const lowerLine = line.toLowerCase()
      if (skillKeywords.some(keyword => lowerLine.includes(keyword))) {
        return `Key skills mentioned: ${line.trim()}`
      }
    }
    return null
  }

  const extractEducation = (text) => {
    const educationKeywords = ['education', 'degree', 'university', 'college', 'b.a.', 'm.a.', 'bachelor', 'master']
    const lines = text.split('\n')
    
    let educationInfo = []
    for (const line of lines) {
      const lowerLine = line.toLowerCase()
      if (educationKeywords.some(keyword => lowerLine.includes(keyword))) {
        educationInfo.push(line.trim())
      }
    }
    
    return educationInfo.length > 0 ? `Education: ${educationInfo.join(', ')}` : null
  }

  const extractCompanies = (text) => {
    const companies = []
    const lines = text.split('\n')
    
    for (const line of lines) {
      if (line.includes('|') && (line.toLowerCase().includes('inc') || line.toLowerCase().includes('corp') || line.toLowerCase().includes('company') || line.toLowerCase().includes('agency'))) {
        const parts = line.split('|')
        if (parts.length >= 2) {
          companies.push(parts[1]?.trim())
        }
      }
    }
    
    return companies.length > 0 ? `Companies worked at: ${companies.join(', ')}` : null
  }

  const extractTeamInfo = (text) => {
    const teamMatch = text.match(/managed?\s*(?:a\s*)?team\s*of\s*(\d+)/i) || 
                     text.match(/(\d+)\s*(?:marketing\s*)?specialists?/i) ||
                     text.match(/team\s*of\s*(\d+)/i)
    
    if (teamMatch) {
      return `Yes, managed a team of ${teamMatch[1]} people.`
    }
    
    if (text.toLowerCase().includes('team') && text.toLowerCase().includes('manage')) {
      return "Yes, has team management experience mentioned in the resume."
    }
    
    return null
  }

  const startConversation = async () => {
    if (!resumeText.trim()) {
      alert('Please paste a resume or upload files first!')
      return
    }

    setConversationStarted(true)
    const currentResumeName = uploadedFiles.length > 0 ? uploadedFiles[selectedResumeIndex].name : 'the resume'
    const fileInfo = uploadedFiles.length > 0 ? ` (Currently analyzing: ${currentResumeName})` : ''

    const initialMessage = {
      type: 'ai',
      content: `Hello! I'm your AI Resume Finder. I've analyzed ${currentResumeName} and I'm ready to answer any questions about it${uploadedFiles.length > 1 ? `. You have ${uploadedFiles.length} resumes uploaded - you can switch between them using the dropdown above` : ''}. What would you like to know about this candidate?`,
      timestamp: new Date()
    }
    setMessages([initialMessage])
  }

  const sendQuestion = async () => {
    if (!currentQuestion.trim()) return
    
    const userMessage = {
      type: 'user',
      content: currentQuestion,
      timestamp: new Date()
    }
    
    setMessages(prev => [...prev, userMessage])
    setCurrentQuestion('')
    
    const aiResponse = await processResumeQuestion(currentQuestion, resumeText)
    
    const aiMessage = {
      type: 'ai',
      content: aiResponse,
      timestamp: new Date()
    }
    
    setMessages(prev => [...prev, aiMessage])
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendQuestion()
    }
  }

  const resetConversation = () => {
    setConversationStarted(false)
    setMessages([])
    setCurrentQuestion('')
    setResumeText('')
    setUploadedFiles([])
    setSelectedResumeIndex(0)
    setInputMethod('text')
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>🤖 Augment Code</h1>
        <p>AI-Powered Conversational Resume Finder</p>
      </header>

      {!conversationStarted ? (
        <div className="setup-section">
          <div className="input-method-selector">
            <h2>Step 1: Choose Input Method</h2>
            <div className="method-buttons">
              <button
                className={`method-button ${inputMethod === 'text' ? 'active' : ''}`}
                onClick={() => setInputMethod('text')}
              >
                📝 Paste Text
              </button>
              <button
                className={`method-button ${inputMethod === 'files' ? 'active' : ''}`}
                onClick={() => setInputMethod('files')}
              >
                📁 Upload Files
              </button>
            </div>
          </div>

          {inputMethod === 'text' ? (
            <div className="resume-input-section">
              <h2>Step 2: Paste Resume Text</h2>
              <textarea
                value={resumeText}
                onChange={(e) => setResumeText(e.target.value)}
                placeholder="Paste the full resume text here..."
                className="resume-textarea"
                rows={15}
              />
            </div>
          ) : (
            <div className="file-upload-section">
              <h2>Step 2: Upload Resume Files or Folders</h2>
              <div className="upload-options">
                <div className="upload-area">
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept=".txt,.docx,.pdf,text/plain,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf"
                    onChange={handleFileUpload}
                    className="file-input-hidden"
                  />
                  <div className="upload-dropzone" onClick={triggerFileUpload}>
                    <div className="upload-icon">📄</div>
                    <p><strong>Click to upload resume files</strong></p>
                    <p className="upload-hint">Supports .txt, .docx, and .pdf files. Select multiple files.</p>
                  </div>
                </div>

                <div className="upload-divider">
                  <span>OR</span>
                </div>

                <div className="folder-upload-area">
                  <input
                    type="file"
                    multiple
                    webkitdirectory=""
                    directory=""
                    onChange={handleFolderUpload}
                    className="file-input-hidden"
                    id="folder-input"
                  />
                  <div className="upload-dropzone folder-dropzone" onClick={() => document.getElementById('folder-input').click()}>
                    <div className="upload-icon">📁</div>
                    <p><strong>Click to upload entire folder</strong></p>
                    <p className="upload-hint">Upload a folder containing resume files (.txt, .docx, .pdf)</p>
                  </div>
                </div>
              </div>

              {isProcessing && (
                <div className="processing-indicator">
                  <div className="spinner"></div>
                  <p>Processing files... Please wait.</p>
                </div>
              )}

              {uploadedFiles.length > 0 && !isProcessing && (
                <div className="uploaded-files">
                  <h3>Uploaded Files ({uploadedFiles.length})</h3>
                  <div className="file-list">
                    {uploadedFiles.map((file, index) => (
                      <div
                        key={index}
                        className={`file-item ${index === selectedResumeIndex ? 'selected' : ''}`}
                        onClick={() => handleResumeSelection(index)}
                      >
                        <div className="file-info">
                          <span className="file-name">
                            {getFileIcon(file.format)} {file.name}
                          </span>
                          <div className="file-details">
                            <span className="file-format">{file.format}</span>
                            <span className="file-size">{(file.size / 1024).toFixed(1)} KB</span>
                          </div>
                        </div>
                        {index === selectedResumeIndex && <span className="selected-indicator">✓</span>}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          <div className="start-section">
            <h2>Step 3: Start Conversation</h2>
            <button
              onClick={startConversation}
              className="start-button"
              disabled={!resumeText.trim()}
            >
              🚀 Start Conversation with Resume Finder
            </button>
          </div>
        </div>
      ) : (
        <div className="conversation-section">
          <div className="conversation-header">
            <h2>💬 Resume Analysis Conversation</h2>
            <div className="header-controls">
              {uploadedFiles.length > 1 && (
                <div className="resume-selector">
                  <label htmlFor="resume-select">Current Resume:</label>
                  <select
                    id="resume-select"
                    value={selectedResumeIndex}
                    onChange={(e) => handleResumeSelection(parseInt(e.target.value))}
                    className="resume-dropdown"
                  >
                    {uploadedFiles.map((file, index) => (
                      <option key={index} value={index}>
                        {file.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}
              <button onClick={resetConversation} className="reset-button">
                🔄 New Resume
              </button>
            </div>
          </div>
          
          <div className="messages-container">
            {messages.map((message, index) => (
              <div key={index} className={`message ${message.type}`}>
                <div className="message-avatar">
                  {message.type === 'ai' ? '🤖' : '👤'}
                </div>
                <div className="message-content">
                  <p>{message.content}</p>
                  <span className="message-time">
                    {message.timestamp.toLocaleTimeString()}
                  </span>
                </div>
              </div>
            ))}
            
            {isProcessing && (
              <div className="message ai">
                <div className="message-avatar">🤖</div>
                <div className="message-content">
                  <p className="typing">Analyzing resume... 💭</p>
                </div>
              </div>
            )}
          </div>
          
          <div className="input-section">
            <div className="question-input-container">
              <input
                type="text"
                value={currentQuestion}
                onChange={(e) => setCurrentQuestion(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask a question about the resume..."
                className="question-input"
                disabled={isProcessing}
              />
              <button 
                onClick={sendQuestion}
                className="send-button"
                disabled={!currentQuestion.trim() || isProcessing}
              >
                📤 Send
              </button>
            </div>
            
            <div className="example-questions">
              <p><strong>Example questions:</strong></p>
              <div className="question-chips">
                <span onClick={() => setCurrentQuestion("What is the candidate's current job title and company?")}>
                  Current job?
                </span>
                <span onClick={() => setCurrentQuestion("How many years of experience does this person have?")}>
                  Years of experience?
                </span>
                <span onClick={() => setCurrentQuestion("What are their key skills?")}>
                  Key skills?
                </span>
                <span onClick={() => setCurrentQuestion("Did they manage a team?")}>
                  Team management?
                </span>
                <span onClick={() => setCurrentQuestion("What is their education background?")}>
                  Education?
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default App

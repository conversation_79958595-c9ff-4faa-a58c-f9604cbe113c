import { useState, useRef } from 'react'
import './App.css'
import mammoth from 'mammoth'
import * as pdfjsLib from 'pdfjs-dist'

// Set up PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js`

function App() {
  const [resumeText, setResumeText] = useState('')
  const [conversationStarted, setConversationStarted] = useState(false)
  const [messages, setMessages] = useState([])
  const [currentQuestion, setCurrentQuestion] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState([])

  const [inputMethod, setInputMethod] = useState('text') // 'text' or 'files'
  const fileInputRef = useRef(null)

  // File handling functions
  const handleFileUpload = async (event) => {
    const files = Array.from(event.target.files)
    const processedFiles = []

    setIsProcessing(true)

    for (const file of files) {
      try {
        let text = ''
        const fileExtension = file.name.toLowerCase().split('.').pop()

        if (fileExtension === 'txt' || file.type === 'text/plain') {
          text = await readFileAsText(file)
        } else if (fileExtension === 'docx' || file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
          text = await readDocxFile(file)
        } else if (fileExtension === 'pdf' || file.type === 'application/pdf') {
          text = await readPdfFile(file)
        } else {
          console.warn(`Unsupported file type: ${file.name}`)
          continue
        }

        if (text.trim()) {
          processedFiles.push({
            name: file.name,
            content: text,
            size: file.size,
            type: file.type,
            format: fileExtension.toUpperCase()
          })
        }
      } catch (error) {
        console.error(`Error reading file ${file.name}:`, error)
      }
    }

    setIsProcessing(false)
    setUploadedFiles(processedFiles)
    if (processedFiles.length > 0) {
      // Combine all resumes into one pool for analysis
      const combinedResumeText = processedFiles.map((file, index) =>
        `=== RESUME ${index + 1}: ${file.name} ===\n${file.content}\n\n`
      ).join('')
      setResumeText(combinedResumeText)
    }
  }

  const readFileAsText = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target.result)
      reader.onerror = (e) => reject(e)
      reader.readAsText(file)
    })
  }

  const readDocxFile = async (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = async (e) => {
        try {
          const arrayBuffer = e.target.result
          const result = await mammoth.extractRawText({ arrayBuffer })
          resolve(result.value)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = (e) => reject(e)
      reader.readAsArrayBuffer(file)
    })
  }

  const readPdfFile = async (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = async (e) => {
        try {
          const arrayBuffer = e.target.result
          const text = await extractTextFromPdfBuffer(arrayBuffer)
          resolve(text)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = (e) => reject(e)
      reader.readAsArrayBuffer(file)
    })
  }

  // PDF text extraction using PDF.js
  const extractTextFromPdfBuffer = async (arrayBuffer) => {
    try {
      const uint8Array = new Uint8Array(arrayBuffer)
      const pdf = await pdfjsLib.getDocument({ data: uint8Array }).promise

      let fullText = ''

      // Extract text from each page
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        const page = await pdf.getPage(pageNum)
        const textContent = await page.getTextContent()

        const pageText = textContent.items
          .map(item => item.str)
          .join(' ')
          .replace(/\s+/g, ' ')
          .trim()

        if (pageText) {
          fullText += pageText + '\n'
        }
      }

      if (fullText.trim().length === 0) {
        throw new Error('No text content found in PDF')
      }

      return fullText.trim()
    } catch (error) {
      console.error('PDF parsing error:', error)
      throw new Error(`PDF parsing failed: ${error.message}. Please ensure the PDF contains selectable text or convert to DOCX/TXT format.`)
    }
  }



  const triggerFileUpload = () => {
    fileInputRef.current?.click()
  }

  const handleFolderUpload = async (event) => {
    const files = Array.from(event.target.files)
    await handleFileUpload({ target: { files } })
  }

  const getFileIcon = (format) => {
    switch (format?.toUpperCase()) {
      case 'PDF':
        return '📕'
      case 'DOCX':
        return '📘'
      case 'TXT':
        return '📄'
      default:
        return '📄'
    }
  }

  // Mock AI Resume Finder - simulates intelligent responses
  const processResumeQuestion = async (question, resumeContext) => {
    setIsProcessing(true)
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Simple AI-like responses based on resume content and question
    let response = generateIntelligentResponse(question, resumeContext)
    
    setIsProcessing(false)
    return response
  }

  // Helper functions for intelligent candidate analysis
  const extractYearsOfExperience = (content) => {
    const expMatch = content.match(/(\d+)\+?\s*years?\s*(of\s*)?experience/i)
    return expMatch ? parseInt(expMatch[1]) : 0
  }

  const extractTechnicalSkills = (content) => {
    const techKeywords = ['react', 'python', 'javascript', 'java', 'node.js', 'sql', 'aws', 'docker', 'kubernetes', 'git', 'agile', 'scrum', 'ci/cd', 'testing', 'selenium', 'jest']
    return techKeywords.filter(skill => content.includes(skill))
  }

  const isComplexHiringQuery = (question) => {
    const complexIndicators = [
      'find', 'looking for', 'need', 'want', 'hire', 'recommend', 'best candidate',
      'who should', 'which candidate', 'ideal candidate', 'perfect fit'
    ]
    return complexIndicators.some(indicator => question.includes(indicator))
  }

  const handleComplexHiringQuery = (question, candidates) => {
    // Calculate match scores based on requirements mentioned in the question
    candidates.forEach(candidate => {
      let score = 0

      // Education requirements
      if (question.includes('bachelor') && candidate.hasBachelorsDegree) score += 20
      if (question.includes('computer science') && candidate.hasComputerScienceDegree) score += 25
      if (question.includes('master') && candidate.hasMastersDegree) score += 15

      // Role requirements
      if ((question.includes('software engineer') || question.includes('developer')) && candidate.isSoftwareEngineer) score += 30

      // Experience requirements
      if (question.includes('testing') && candidate.hasTestingExperience) score += 20
      if (question.includes('interview') && candidate.hasInterviewingExperience) score += 15
      if (question.includes('leadership') && candidate.hasLeadershipExperience) score += 15

      // Years of experience
      if (question.includes('senior') && candidate.yearsOfExperience >= 5) score += 20
      if (question.includes('experienced') && candidate.yearsOfExperience >= 3) score += 15

      // Technical skills bonus
      score += candidate.technicalSkills.length * 3

      // Experience bonus
      score += Math.min(candidate.yearsOfExperience * 2, 20)

      candidate.matchScore = score
    })

    // Sort by match score
    const rankedCandidates = candidates.sort((a, b) => b.matchScore - a.matchScore)
    const topCandidates = rankedCandidates.slice(0, 3).filter(c => c.matchScore > 0)

    if (topCandidates.length === 0) {
      return `**No Perfect Matches Found**\n\nBased on your requirements, none of the candidates perfectly match all criteria. Here are the closest matches:\n\n${rankedCandidates.slice(0, 2).map((c, i) =>
        `${i + 1}. **${c.name}** (${c.matchScore}% match)\n   - ${c.yearsOfExperience}+ years experience\n   - Skills: ${c.technicalSkills.join(', ') || 'General experience'}`
      ).join('\n\n')}\n\n*Consider adjusting requirements or providing additional training.*`
    }

    const bestCandidate = topCandidates[0]
    let recommendation = `**🎯 HIRING RECOMMENDATION**\n\n`

    if (bestCandidate.matchScore >= 70) {
      recommendation += `**STRONG MATCH: ${bestCandidate.name}** (${bestCandidate.matchScore}% match)\n\n`
      recommendation += `**Why this candidate is ideal:**\n`
    } else {
      recommendation += `**BEST AVAILABLE: ${bestCandidate.name}** (${bestCandidate.matchScore}% match)\n\n`
      recommendation += `**Why this candidate is your best option:**\n`
    }

    // Detailed reasoning
    const reasons = []
    if (bestCandidate.hasComputerScienceDegree) reasons.push("✅ Computer Science degree")
    if (bestCandidate.hasBachelorsDegree) reasons.push("✅ Bachelor's degree")
    if (bestCandidate.isSoftwareEngineer) reasons.push("✅ Software engineering background")
    if (bestCandidate.hasTestingExperience) reasons.push("✅ Testing experience")
    if (bestCandidate.hasInterviewingExperience) reasons.push("✅ Interviewing/hiring experience")
    if (bestCandidate.hasLeadershipExperience) reasons.push("✅ Leadership experience")
    if (bestCandidate.yearsOfExperience >= 5) reasons.push(`✅ ${bestCandidate.yearsOfExperience}+ years of experience`)
    if (bestCandidate.technicalSkills.length > 0) reasons.push(`✅ Technical skills: ${bestCandidate.technicalSkills.join(', ')}`)

    recommendation += reasons.join('\n') + '\n\n'

    if (topCandidates.length > 1) {
      recommendation += `**Alternative candidates:**\n`
      recommendation += topCandidates.slice(1).map((c, i) =>
        `${i + 2}. **${c.name}** (${c.matchScore}% match) - ${c.yearsOfExperience}+ years experience`
      ).join('\n')
    }

    return recommendation
  }

  const generatePoolAnalysisResponse = (question, combinedResumeText) => {
    const lowerQuestion = question.toLowerCase()
    const resumes = combinedResumeText.split('=== RESUME').filter(r => r.trim())

    // Parse individual resumes with detailed analysis
    const candidates = resumes.map((resume, index) => {
      const lines = resume.split('\n')
      const nameMatch = lines.find(line => line.includes(':'))?.split(':')[1]?.trim() || `Candidate ${index + 1}`
      const content = resume.toLowerCase()

      // Extract detailed candidate information
      const analysis = {
        name: nameMatch,
        content: content,
        originalContent: resume,

        // Education analysis
        hasComputerScienceDegree: content.includes('computer science') || content.includes('cs degree') || content.includes('computer engineering'),
        hasBachelorsDegree: content.includes('bachelor') || content.includes('bs ') || content.includes('b.s.'),
        hasMastersDegree: content.includes('master') || content.includes('ms ') || content.includes('m.s.'),

        // Experience analysis
        yearsOfExperience: extractYearsOfExperience(content),
        isSoftwareEngineer: content.includes('software engineer') || content.includes('developer') || content.includes('programmer'),
        hasTestingExperience: content.includes('testing') || content.includes('qa') || content.includes('quality assurance') || content.includes('test'),
        hasInterviewingExperience: content.includes('interview') || content.includes('hiring') || content.includes('recruit') || content.includes('candidate evaluation'),
        hasLeadershipExperience: content.includes('lead') || content.includes('senior') || content.includes('manager') || content.includes('director') || content.includes('team lead'),

        // Technical skills
        technicalSkills: extractTechnicalSkills(content),

        // Company experience
        hasLargeCompanyExperience: content.includes('corp') || content.includes('inc') || content.includes('global') || content.includes('enterprise') || content.includes('fortune'),

        // Calculate overall match score for complex queries
        matchScore: 0
      }

      return analysis
    })

    // Handle complex hiring queries with intelligent matching
    if (isComplexHiringQuery(lowerQuestion)) {
      return handleComplexHiringQuery(lowerQuestion, candidates)
    }

    if (lowerQuestion.includes('most experience') || lowerQuestion.includes('years of experience')) {
      const experiencedCandidates = candidates.map(candidate => {
        const expMatch = candidate.content.match(/(\d+)\+?\s*years?\s*(of\s*)?experience/i)
        const years = expMatch ? parseInt(expMatch[1]) : 0
        return { ...candidate, years }
      }).sort((a, b) => b.years - a.years)

      return `**Most Experienced Candidates:**\n\n${experiencedCandidates.slice(0, 3).map((c, i) =>
        `${i + 1}. **${c.name}** - ${c.years}+ years of experience`
      ).join('\n')}`
    }

    if (lowerQuestion.includes('react') || lowerQuestion.includes('python') || lowerQuestion.includes('aws') || lowerQuestion.includes('tech')) {
      const techKeywords = ['react', 'python', 'aws', 'javascript', 'node.js', 'sql', 'java', 'docker', 'kubernetes']
      const techCandidates = candidates.map(candidate => {
        const skills = techKeywords.filter(skill => candidate.content.includes(skill))
        return { ...candidate, skills }
      }).filter(c => c.skills.length > 0)

      return `**Candidates with Technical Skills:**\n\n${techCandidates.map((c, i) =>
        `${i + 1}. **${c.name}** - Skills: ${c.skills.join(', ')}`
      ).join('\n')}`
    }

    if (lowerQuestion.includes('leadership') || lowerQuestion.includes('management') || lowerQuestion.includes('team')) {
      const leadershipCandidates = candidates.filter(candidate =>
        candidate.content.includes('lead') || candidate.content.includes('manage') ||
        candidate.content.includes('senior') || candidate.content.includes('director')
      )

      return `**Candidates with Leadership Experience:**\n\n${leadershipCandidates.map((c, i) =>
        `${i + 1}. **${c.name}** - Has leadership/management experience`
      ).join('\n')}`
    }

    if (lowerQuestion.includes('software developer') || lowerQuestion.includes('developer role')) {
      const devCandidates = candidates.filter(candidate =>
        candidate.content.includes('developer') || candidate.content.includes('software') ||
        candidate.content.includes('programming') || candidate.content.includes('coding')
      )

      return `**Best Candidates for Software Developer Role:**\n\n${devCandidates.map((c, i) =>
        `${i + 1}. **${c.name}** - Has software development background`
      ).join('\n')}`
    }

    if (lowerQuestion.includes('fortune 500') || lowerQuestion.includes('big company') || lowerQuestion.includes('large company')) {
      const bigCompanyCandidates = candidates.filter(candidate =>
        candidate.content.includes('corp') || candidate.content.includes('inc') ||
        candidate.content.includes('global') || candidate.content.includes('enterprise')
      )

      return `**Candidates with Big Company Experience:**\n\n${bigCompanyCandidates.map((c, i) =>
        `${i + 1}. **${c.name}** - Has corporate/enterprise experience`
      ).join('\n')}`
    }

    // Handle other specific queries
    if (lowerQuestion.includes('strongest') || lowerQuestion.includes('best overall')) {
      const strongestCandidates = candidates.map(candidate => {
        const score = candidate.yearsOfExperience * 2 + candidate.technicalSkills.length * 3 +
                     (candidate.hasLeadershipExperience ? 10 : 0) + (candidate.hasBachelorsDegree ? 5 : 0)
        return { ...candidate, overallScore: score }
      }).sort((a, b) => b.overallScore - a.overallScore)

      return `**Strongest Overall Candidates:**\n\n${strongestCandidates.slice(0, 3).map((c, i) =>
        `${i + 1}. **${c.name}** (Overall Score: ${c.overallScore})\n   - ${c.yearsOfExperience}+ years experience\n   - Skills: ${c.technicalSkills.join(', ') || 'General experience'}\n   - ${c.hasLeadershipExperience ? 'Leadership experience' : 'Individual contributor'}`
      ).join('\n\n')}`
    }

    // Default pool summary with intelligent suggestions
    return `**Resume Pool Summary:**\n\nI'm analyzing ${candidates.length} candidates and ready to make hiring recommendations!\n\n**Try asking complex questions like:**\n• "Find me a software engineer with a bachelor's degree in computer science with experience in testing"\n• "I need a senior developer with 5+ years who can lead a team"\n• "Who would be best for a technical lead position?"\n• "Find someone with both development and testing experience"\n\n**Or ask about specific criteria:**\n• Which candidates have the most experience?\n• Who has the strongest technical background?\n• Which candidates have leadership experience?\n\n*I'll analyze all candidates and recommend the best matches with detailed reasoning!*`
  }

  const generateIntelligentResponse = (question, resumeText) => {
    const lowerQuestion = question.toLowerCase()
    const resume = resumeText.toLowerCase()

    // Check if this is a multi-resume pool analysis
    const isPoolAnalysis = resumeText.includes('=== RESUME') && uploadedFiles.length > 1

    if (isPoolAnalysis) {
      return generatePoolAnalysisResponse(question, resumeText)
    }

    // Extract key information from resume
    const extractInfo = {
      name: extractName(resumeText),
      currentJob: extractCurrentJob(resumeText),
      experience: extractExperience(resumeText),
      skills: extractSkills(resumeText),
      education: extractEducation(resumeText),
      companies: extractCompanies(resumeText)
    }

    // Generate contextual responses
    if (lowerQuestion.includes('name')) {
      return extractInfo.name ? `The candidate's name is ${extractInfo.name}.` : "I couldn't find a clear name in the resume."
    }
    
    if (lowerQuestion.includes('current job') || lowerQuestion.includes('current position') || lowerQuestion.includes('current role')) {
      return extractInfo.currentJob || "I couldn't determine the current job position from the resume."
    }
    
    if (lowerQuestion.includes('experience') || lowerQuestion.includes('years')) {
      return extractInfo.experience || "I found experience information but couldn't determine specific years."
    }
    
    if (lowerQuestion.includes('skill') || lowerQuestion.includes('proficient') || lowerQuestion.includes('technology')) {
      return extractInfo.skills || "I found various skills mentioned in the resume."
    }
    
    if (lowerQuestion.includes('education') || lowerQuestion.includes('degree') || lowerQuestion.includes('university') || lowerQuestion.includes('college')) {
      return extractInfo.education || "I found education information in the resume."
    }
    
    if (lowerQuestion.includes('team') || lowerQuestion.includes('manage') || lowerQuestion.includes('lead')) {
      const teamInfo = extractTeamInfo(resumeText)
      return teamInfo || "I couldn't find specific information about team management in the resume."
    }
    
    if (lowerQuestion.includes('company') || lowerQuestion.includes('companies') || lowerQuestion.includes('employer')) {
      return extractInfo.companies || "I found several companies mentioned in the work history."
    }
    
    // Default intelligent response
    return `Based on the resume provided, I can help you find specific information. The resume contains details about the candidate's background, experience, and qualifications. Could you ask a more specific question about what you'd like to know?`
  }

  // Helper functions to extract information
  const extractName = (text) => {
    const lines = text.split('\n')
    const firstLine = lines[0]?.trim()
    if (firstLine && firstLine.length < 50 && !firstLine.includes('@') && !firstLine.includes('|')) {
      return firstLine
    }
    return null
  }

  const extractCurrentJob = (text) => {
    const lines = text.split('\n')
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].toLowerCase()
      if (line.includes('present') || line.includes('current')) {
        // Look for job title in previous lines
        for (let j = i - 1; j >= Math.max(0, i - 3); j--) {
          const prevLine = lines[j]?.trim()
          if (prevLine && prevLine.includes('|')) {
            const parts = prevLine.split('|')
            return `Currently working as ${parts[0]?.trim()} at ${parts[1]?.trim()}`
          }
        }
      }
    }
    return null
  }

  const extractExperience = (text) => {
    const experienceMatch = text.match(/(\d+)\+?\s*years?\s*(of\s*)?experience/i)
    if (experienceMatch) {
      return `The candidate has ${experienceMatch[1]}+ years of experience.`
    }
    return null
  }

  const extractSkills = (text) => {
    const skillKeywords = ['proficient', 'skills', 'technologies', 'expertise']
    const lines = text.split('\n')
    
    for (const line of lines) {
      const lowerLine = line.toLowerCase()
      if (skillKeywords.some(keyword => lowerLine.includes(keyword))) {
        return `Key skills mentioned: ${line.trim()}`
      }
    }
    return null
  }

  const extractEducation = (text) => {
    const educationKeywords = ['education', 'degree', 'university', 'college', 'b.a.', 'm.a.', 'bachelor', 'master']
    const lines = text.split('\n')
    
    let educationInfo = []
    for (const line of lines) {
      const lowerLine = line.toLowerCase()
      if (educationKeywords.some(keyword => lowerLine.includes(keyword))) {
        educationInfo.push(line.trim())
      }
    }
    
    return educationInfo.length > 0 ? `Education: ${educationInfo.join(', ')}` : null
  }

  const extractCompanies = (text) => {
    const companies = []
    const lines = text.split('\n')
    
    for (const line of lines) {
      if (line.includes('|') && (line.toLowerCase().includes('inc') || line.toLowerCase().includes('corp') || line.toLowerCase().includes('company') || line.toLowerCase().includes('agency'))) {
        const parts = line.split('|')
        if (parts.length >= 2) {
          companies.push(parts[1]?.trim())
        }
      }
    }
    
    return companies.length > 0 ? `Companies worked at: ${companies.join(', ')}` : null
  }

  const extractTeamInfo = (text) => {
    const teamMatch = text.match(/managed?\s*(?:a\s*)?team\s*of\s*(\d+)/i) || 
                     text.match(/(\d+)\s*(?:marketing\s*)?specialists?/i) ||
                     text.match(/team\s*of\s*(\d+)/i)
    
    if (teamMatch) {
      return `Yes, managed a team of ${teamMatch[1]} people.`
    }
    
    if (text.toLowerCase().includes('team') && text.toLowerCase().includes('manage')) {
      return "Yes, has team management experience mentioned in the resume."
    }
    
    return null
  }

  const startConversation = async () => {
    if (!resumeText.trim()) {
      alert('Please paste a resume or upload files first!')
      return
    }

    setConversationStarted(true)
    const currentResumeName = uploadedFiles.length > 0 ? uploadedFiles[selectedResumeIndex].name : 'the resume'
    const fileInfo = uploadedFiles.length > 0 ? ` (Currently analyzing: ${currentResumeName})` : ''

    const initialMessage = {
      type: 'ai',
      content: `Hello! I'm your AI Resume Finder. I've analyzed ${currentResumeName} and I'm ready to answer any questions about it${uploadedFiles.length > 1 ? `. You have ${uploadedFiles.length} resumes uploaded - you can switch between them using the dropdown above` : ''}. What would you like to know about this candidate?`,
      timestamp: new Date()
    }
    setMessages([initialMessage])
  }

  const sendQuestion = async () => {
    if (!currentQuestion.trim()) return
    
    const userMessage = {
      type: 'user',
      content: currentQuestion,
      timestamp: new Date()
    }
    
    setMessages(prev => [...prev, userMessage])
    setCurrentQuestion('')
    
    const aiResponse = await processResumeQuestion(currentQuestion, resumeText)
    
    const aiMessage = {
      type: 'ai',
      content: aiResponse,
      timestamp: new Date()
    }
    
    setMessages(prev => [...prev, aiMessage])
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendQuestion()
    }
  }

  const resetConversation = () => {
    setConversationStarted(false)
    setMessages([])
    setCurrentQuestion('')
    setResumeText('')
    setUploadedFiles([])
    setSelectedResumeIndex(0)
    setInputMethod('text')
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>🤖 Augment Code</h1>
        <p>AI-Powered Conversational Resume Finder</p>
      </header>

      {!conversationStarted ? (
        <div className="setup-section">
          <div className="input-method-selector">
            <h2>Step 1: Choose Input Method</h2>
            <div className="method-buttons">
              <button
                className={`method-button ${inputMethod === 'text' ? 'active' : ''}`}
                onClick={() => setInputMethod('text')}
              >
                📝 Paste Text
              </button>
              <button
                className={`method-button ${inputMethod === 'files' ? 'active' : ''}`}
                onClick={() => setInputMethod('files')}
              >
                📁 Upload Files
              </button>
            </div>
          </div>

          {inputMethod === 'text' ? (
            <div className="resume-input-section">
              <h2>Step 2: Paste Resume Text</h2>
              <textarea
                value={resumeText}
                onChange={(e) => setResumeText(e.target.value)}
                placeholder="Paste the full resume text here..."
                className="resume-textarea"
                rows={15}
              />
            </div>
          ) : (
            <div className="file-upload-section">
              <h2>Step 2: Upload Resume Files or Folders</h2>
              <div className="upload-options">
                <div className="upload-area">
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept=".txt,.docx,.pdf,text/plain,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf"
                    onChange={handleFileUpload}
                    className="file-input-hidden"
                  />
                  <div className="upload-dropzone" onClick={triggerFileUpload}>
                    <div className="upload-icon">📄</div>
                    <p><strong>Click to upload resume files</strong></p>
                    <p className="upload-hint">Supports .txt, .docx, and .pdf files. Select multiple files.</p>
                  </div>
                </div>

                <div className="upload-divider">
                  <span>OR</span>
                </div>

                <div className="folder-upload-area">
                  <input
                    type="file"
                    multiple
                    webkitdirectory=""
                    directory=""
                    onChange={handleFolderUpload}
                    className="file-input-hidden"
                    id="folder-input"
                  />
                  <div className="upload-dropzone folder-dropzone" onClick={() => document.getElementById('folder-input').click()}>
                    <div className="upload-icon">📁</div>
                    <p><strong>Click to upload entire folder</strong></p>
                    <p className="upload-hint">Upload a folder containing resume files (.txt, .docx, .pdf)</p>
                  </div>
                </div>
              </div>

              {isProcessing && (
                <div className="processing-indicator">
                  <div className="spinner"></div>
                  <p>Processing files... Please wait.</p>
                </div>
              )}

              {uploadedFiles.length > 0 && !isProcessing && (
                <div className="uploaded-files">
                  <h3>Resume Pool ({uploadedFiles.length} candidates)</h3>
                  <p className="pool-description">
                    All resumes have been combined into a searchable pool. Ask questions about the entire candidate pool!
                  </p>
                  <div className="file-list">
                    {uploadedFiles.map((file, index) => (
                      <div
                        key={index}
                        className="file-item pool-item"
                      >
                        <div className="file-info">
                          <span className="file-name">
                            {getFileIcon(file.format)} {file.name}
                          </span>
                          <div className="file-details">
                            <span className="file-format">{file.format}</span>
                            <span className="file-size">{(file.size / 1024).toFixed(1)} KB</span>
                          </div>
                        </div>
                        <span className="pool-indicator">📋</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          <div className="start-section">
            <h2>Step 3: Start Conversation</h2>
            <button
              onClick={startConversation}
              className="start-button"
              disabled={!resumeText.trim()}
            >
              🚀 Start Conversation with Resume Finder
            </button>
          </div>
        </div>
      ) : (
        <div className="conversation-section">
          <div className="conversation-header">
            <h2>💬 Resume Pool Analysis</h2>
            <div className="header-controls">
              {uploadedFiles.length > 0 && (
                <div className="pool-info">
                  <span className="pool-count">Analyzing {uploadedFiles.length} candidates</span>
                </div>
              )}
              <button onClick={resetConversation} className="reset-button">
                🔄 Reset Pool
              </button>
            </div>
          </div>
          
          <div className="messages-container">
            {messages.map((message, index) => (
              <div key={index} className={`message ${message.type}`}>
                <div className="message-avatar">
                  {message.type === 'ai' ? '🤖' : '👤'}
                </div>
                <div className="message-content">
                  <p>{message.content}</p>
                  <span className="message-time">
                    {message.timestamp.toLocaleTimeString()}
                  </span>
                </div>
              </div>
            ))}
            
            {isProcessing && (
              <div className="message ai">
                <div className="message-avatar">🤖</div>
                <div className="message-content">
                  <p className="typing">Analyzing resume... 💭</p>
                </div>
              </div>
            )}
          </div>
          
          <div className="input-section">
            <div className="question-input-container">
              <input
                type="text"
                value={currentQuestion}
                onChange={(e) => setCurrentQuestion(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask a question about the resume..."
                className="question-input"
                disabled={isProcessing}
              />
              <button 
                onClick={sendQuestion}
                className="send-button"
                disabled={!currentQuestion.trim() || isProcessing}
              >
                📤 Send
              </button>
            </div>
            
            <div className="example-questions">
              <p><strong>Example questions for resume pool analysis:</strong></p>
              <div className="question-chips">
                {uploadedFiles.length > 1 ? (
                  <>
                    <span onClick={() => setCurrentQuestion("Find me a software engineer with a bachelor's degree in computer science with experience in software testing and interviewing candidates")}>
                      Complex hiring query
                    </span>
                    <span onClick={() => setCurrentQuestion("I need a senior developer with 5+ years experience who can lead a team")}>
                      Senior developer
                    </span>
                    <span onClick={() => setCurrentQuestion("Who would be the best candidate for a technical lead position?")}>
                      Technical lead
                    </span>
                    <span onClick={() => setCurrentQuestion("Find someone with both development and testing experience")}>
                      Dev + Testing
                    </span>
                    <span onClick={() => setCurrentQuestion("Which candidate has the strongest technical background?")}>
                      Strongest tech
                    </span>
                  </>
                ) : (
                  <>
                    <span onClick={() => setCurrentQuestion("What is the candidate's current job title and company?")}>
                      Current job?
                    </span>
                    <span onClick={() => setCurrentQuestion("How many years of experience does this person have?")}>
                      Years of experience?
                    </span>
                    <span onClick={() => setCurrentQuestion("What are their key skills?")}>
                      Key skills?
                    </span>
                    <span onClick={() => setCurrentQuestion("Did they manage a team?")}>
                      Team management?
                    </span>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default App

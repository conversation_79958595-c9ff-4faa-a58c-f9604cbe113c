.app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.app-header {
  margin-bottom: 2rem;
}

.app-header h1 {
  color: #646cff;
  margin-bottom: 0.5rem;
}

.app-header p {
  color: #888;
  font-size: 1.1rem;
}

/* Setup Section */
.setup-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

/* Input Method Selector */
.input-method-selector {
  text-align: center;
}

.input-method-selector h2 {
  color: #333;
  margin-bottom: 1rem;
}

.method-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1rem;
}

.method-button {
  background: #f0f0f0;
  color: #333;
  border: 2px solid #ddd;
  padding: 1rem 2rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
  min-width: 150px;
}

.method-button:hover {
  background: #e0e0e0;
  border-color: #ccc;
}

.method-button.active {
  background: #646cff;
  color: white;
  border-color: #646cff;
}

.method-button.active:hover {
  background: #535bf2;
  border-color: #535bf2;
}

.resume-input-section h2,
.start-section h2 {
  color: #333;
  margin-bottom: 1rem;
}

.resume-textarea {
  width: 100%;
  min-height: 300px;
  padding: 1rem;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  resize: vertical;
  background-color: #f9f9f9;
}

.resume-textarea:focus {
  outline: none;
  border-color: #646cff;
  background-color: white;
}

.start-button {
  background: linear-gradient(135deg, #646cff, #535bf2);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.start-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(100, 108, 255, 0.3);
}

.start-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* File Upload Section */
.file-upload-section h2 {
  color: #333;
  margin-bottom: 1rem;
  text-align: center;
}

.upload-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.upload-area, .folder-upload-area {
  flex: 1;
}

.upload-divider {
  text-align: center;
  position: relative;
  margin: 1rem 0;
}

.upload-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #ddd;
  z-index: 1;
}

.upload-divider span {
  background: white;
  padding: 0 1rem;
  color: #666;
  font-size: 0.9rem;
  position: relative;
  z-index: 2;
}

.file-input-hidden {
  display: none;
}

.upload-dropzone {
  border: 3px dashed #646cff;
  border-radius: 12px;
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9ff;
}

.upload-dropzone:hover {
  border-color: #535bf2;
  background: #f0f2ff;
  transform: translateY(-2px);
}

.folder-dropzone {
  border-color: #4caf50;
  background: #f8fff8;
}

.folder-dropzone:hover {
  border-color: #45a049;
  background: #f0fff0;
}

.upload-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.upload-dropzone p {
  margin: 0.5rem 0;
  color: #333;
}

.upload-hint {
  font-size: 0.9rem;
  color: #666;
}

/* Processing Indicator */
.processing-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 2rem;
  background: #f9f9f9;
  border-radius: 8px;
  margin: 1rem 0;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #646cff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-indicator p {
  margin: 0;
  color: #666;
  font-style: italic;
}

/* Uploaded Files */
.uploaded-files {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.uploaded-files h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: white;
  border: 2px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-item:hover {
  border-color: #646cff;
  background: #f8f9ff;
}

.file-item.selected {
  border-color: #646cff;
  background: #646cff;
  color: white;
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.file-name {
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.file-details {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  opacity: 0.7;
}

.file-format {
  background: #e0e0e0;
  color: #333;
  padding: 0.1rem 0.4rem;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 500;
}

.file-size {
  font-size: 0.8rem;
}

.selected-indicator {
  font-weight: bold;
  color: white;
}

/* Conversation Section */
.conversation-section {
  max-width: 900px;
  margin: 0 auto;
  text-align: left;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #eee;
}

.conversation-header h2 {
  color: #333;
  margin: 0;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.resume-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.resume-selector label {
  font-size: 0.9rem;
  color: #666;
  white-space: nowrap;
}

.resume-dropdown {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  font-size: 0.9rem;
  min-width: 200px;
}

.resume-dropdown:focus {
  outline: none;
  border-color: #646cff;
}

.reset-button {
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  white-space: nowrap;
}

.reset-button:hover {
  background: #ff5252;
}

/* Messages */
.messages-container {
  height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  background-color: #fafafa;
}

.message {
  display: flex;
  margin-bottom: 1rem;
  animation: fadeIn 0.3s ease-in;
}

.message.user {
  justify-content: flex-end;
}

.message.ai {
  justify-content: flex-start;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  margin: 0 0.5rem;
  flex-shrink: 0;
}

.message.user .message-avatar {
  background: #646cff;
  order: 2;
}

.message.ai .message-avatar {
  background: #4caf50;
  order: 1;
}

.message-content {
  max-width: 70%;
  padding: 0.75rem 1rem;
  border-radius: 18px;
  position: relative;
}

.message.user .message-content {
  background: #646cff;
  color: white;
  border-bottom-right-radius: 4px;
}

.message.ai .message-content {
  background: white;
  color: #333;
  border: 1px solid #ddd;
  border-bottom-left-radius: 4px;
}

.message-content p {
  margin: 0;
  line-height: 1.4;
}

.message-time {
  font-size: 0.7rem;
  opacity: 0.7;
  display: block;
  margin-top: 0.25rem;
}

.typing {
  font-style: italic;
  opacity: 0.8;
}

/* Input Section */
.input-section {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1rem;
}

.question-input-container {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.question-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
}

.question-input:focus {
  outline: none;
  border-color: #646cff;
}

.send-button {
  background: #4caf50;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
}

.send-button:hover:not(:disabled) {
  background: #45a049;
}

.send-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Example Questions */
.example-questions {
  text-align: left;
}

.example-questions p {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: #666;
}

.question-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.question-chips span {
  background: #f0f0f0;
  color: #333;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #ddd;
}

.question-chips span:hover {
  background: #646cff;
  color: white;
  border-color: #646cff;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    padding: 1rem;
  }

  .method-buttons {
    flex-direction: column;
    align-items: center;
  }

  .method-button {
    width: 100%;
    max-width: 250px;
  }

  .conversation-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .header-controls {
    flex-direction: column;
    width: 100%;
  }

  .resume-selector {
    flex-direction: column;
    width: 100%;
  }

  .resume-dropdown {
    width: 100%;
  }

  .question-input-container {
    flex-direction: column;
  }

  .message-content {
    max-width: 85%;
  }

  .question-chips {
    justify-content: center;
  }

  .upload-options {
    gap: 1.5rem;
  }

  .upload-dropzone {
    padding: 2rem 1rem;
  }

  .file-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .file-details {
    flex-direction: column;
    gap: 0.25rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .resume-textarea {
    background-color: #2a2a2a;
    color: white;
    border-color: #555;
  }
  
  .messages-container {
    background-color: #1a1a1a;
    border-color: #555;
  }
  
  .message.ai .message-content {
    background: #2a2a2a;
    color: white;
    border-color: #555;
  }
  
  .input-section {
    background: #2a2a2a;
    border-color: #555;
  }
  
  .question-input {
    background: #1a1a1a;
    color: white;
    border-color: #555;
  }
  
  .question-chips span {
    background: #333;
    color: white;
    border-color: #555;
  }
}

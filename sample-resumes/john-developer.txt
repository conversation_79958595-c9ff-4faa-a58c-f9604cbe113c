<PERSON>
Senior Software Developer
<EMAIL> | (555) 123-4567 | GitHub: github.com/johnsmith | LinkedIn: linkedin.com/in/johnsmith

Summary:
Experienced Senior Software Developer with 8+ years of expertise in full-stack development, cloud architecture, and team leadership. Proven track record of delivering scalable web applications and leading development teams. Proficient in JavaScript, Python, React, Node.js, and AWS.

Experience:
Senior Software Developer | TechCorp Solutions | Jan 2021 - Present
- Lead a team of 5 developers in building enterprise-level web applications using React and Node.js
- Architected and implemented microservices infrastructure on AWS, reducing system downtime by 60%
- Mentored junior developers and conducted code reviews to maintain high code quality standards
- Collaborated with product managers and designers to deliver features ahead of schedule

Software Developer | StartupXYZ | Mar 2018 - Dec 2020
- Developed and maintained full-stack web applications using JavaScript, Python, and PostgreSQL
- Implemented automated testing strategies that increased code coverage from 40% to 85%
- Optimized database queries and application performance, improving load times by 45%
- Participated in agile development processes and sprint planning

Junior Developer | WebDev Agency | Jun 2016 - Feb 2018
- Built responsive websites and web applications for various clients
- Worked with HTML, CSS, JavaScript, and PHP to create dynamic user interfaces
- Collaborated with design team to implement pixel-perfect UI components

Education:
B.S. in Computer Science | State University | 2016
Relevant Coursework: Data Structures, Algorithms, Database Systems, Software Engineering

Certifications:
- AWS Certified Solutions Architect
- Certified Scrum Master (CSM)
- Google Cloud Professional Developer

Technical Skills:
- Languages: JavaScript, Python, TypeScript, Java, PHP
- Frontend: React, Vue.js, Angular, HTML5, CSS3, SASS
- Backend: Node.js, Express, Django, Flask, Spring Boot
- Databases: PostgreSQL, MySQL, MongoDB, Redis
- Cloud: AWS, Google Cloud, Azure, Docker, Kubernetes
- Tools: Git, Jenkins, JIRA, Slack, VS Code
